<?php

namespace App\Helpers;

class LogoHelper
{
    /**
     * Processa logo de forma segura para uso em views Blade
     * Retorna base64 da imagem ou null se não existir
     */
    public static function processarLogoSeguroBase64($nomeArquivo)
    {
        if (!$nomeArquivo) {
            return null;
        }

        $caminhoLogo = public_path('logos/' . $nomeArquivo);
        
        // Verificar se arquivo existe
        if (!file_exists($caminhoLogo)) {
            \Log::warning("Logo não encontrada: " . $caminhoLogo);
            return null;
        }

        try {
            // Tentar ler o arquivo
            $conteudo = file_get_contents($caminhoLogo);
            if ($conteudo === false) {
                \Log::warning("Erro ao ler logo: " . $caminhoLogo);
                return null;
            }

            // Verificar se é uma imagem válida
            $info = getimagesize($caminhoLogo);
            if ($info === false) {
                \Log::warning("Arquivo não é uma imagem válida: " . $caminhoLogo);
                return null;
            }

            // Retornar base64 da imagem
            return 'data:image/png;base64,' . base64_encode($conteudo);
            
        } catch (\Exception $e) {
            \Log::error("Erro ao processar logo: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Retorna logo padrão do sistema
     */
    public static function logosPadrao()
    {
        $caminhoLogoPadrao = public_path('imgs/slym.png');
        
        if (file_exists($caminhoLogoPadrao)) {
            try {
                $conteudo = file_get_contents($caminhoLogoPadrao);
                if ($conteudo !== false) {
                    return 'data:image/png;base64,' . base64_encode($conteudo);
                }
            } catch (\Exception $e) {
                \Log::error("Erro ao processar logo padrão: " . $e->getMessage());
            }
        }
        
        return null;
    }
}
