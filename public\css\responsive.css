/*
 * Arquivo CSS de Responsividade para o Sistema
 * Criado para melhorar a experiência em dispositivos móveis
 */

/* ========================================
   BREAKPOINTS PRINCIPAIS
   ======================================== */
/*
   xs: 0-575px (Celulares pequenos)
   sm: 576-767px (Celulares grandes)
   md: 768-991px (Tablets)
   lg: 992-1199px (Desktops pequenos)
   xl: 1200px+ (Desktops grandes)
*/

/* ========================================
   UTILITÁRIOS RESPONSIVOS
   ======================================== */
.mobile-only {
    display: none !important;
}

.desktop-only {
    display: block !important;
}

@media (max-width: 991.98px) {
    .mobile-only {
        display: block !important;
    }

    .desktop-only {
        display: none !important;
    }
}

/* ========================================
   SIDEBAR E NAVEGAÇÃO MOBILE
   ======================================== */
@media (max-width: 991.98px) {
    /* Ajustes para o header mobile */
    .header-mobile {
        padding: 0 15px;
        min-height: 60px;
        height: 60px;
    }

    .header-mobile .brand-logo img {
        max-width: 100px;
        height: auto;
    }

    /* Ajustes para o wrapper quando header mobile está fixo */
    .header-mobile-fixed .wrapper {
        padding-top: 60px !important;
    }

    /* Menu mobile melhorado */
    .aside-menu-wrapper {
        padding: 10px 0;
    }

    .menu-nav > .menu-item > .menu-link {
        padding: 12px 20px;
        min-height: 48px;
    }

    .menu-nav > .menu-item > .menu-link .menu-text {
        font-size: 1rem;
    }
}

/* ========================================
   TABELAS RESPONSIVAS
   ======================================== */
.table-responsive-custom {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 767.98px) {
    .table-responsive-custom {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .table-responsive-custom .table {
        margin-bottom: 0;
        min-width: 600px; /* Largura mínima para scroll horizontal */
    }

    .table-responsive-custom .table th,
    .table-responsive-custom .table td {
        white-space: nowrap;
        padding: 8px 12px;
        font-size: 0.875rem;
    }

    /* Tabelas empilhadas para mobile */
    .table-stacked {
        min-width: auto !important;
    }

    .table-stacked thead {
        display: none;
    }

    .table-stacked tbody tr {
        display: block;
        border: 1px solid #dee2e6;
        margin-bottom: 10px;
        border-radius: 0.375rem;
        padding: 10px;
    }

    .table-stacked tbody td {
        display: block;
        text-align: right;
        border: none;
        padding: 5px 0;
        position: relative;
        padding-left: 50%;
    }

    .table-stacked tbody td:before {
        content: attr(data-label);
        position: absolute;
        left: 6px;
        width: 45%;
        text-align: left;
        font-weight: bold;
        color: #495057;
    }
}

/* ========================================
   FORMULÁRIOS RESPONSIVOS
   ======================================== */
@media (max-width: 767.98px) {
    .form-group {
        margin-bottom: 1rem;
    }

    .form-control {
        font-size: 16px; /* Evita zoom no iOS */
        padding: 12px 15px;
        min-height: 48px; /* Melhor área de toque */
    }

    .form-control-sm {
        font-size: 14px;
        padding: 8px 12px;
        min-height: 40px;
    }

    /* Botões responsivos */
    .btn {
        min-height: 44px;
        padding: 10px 20px;
        font-size: 1rem;
    }

    .btn-sm {
        min-height: 36px;
        padding: 6px 12px;
        font-size: 0.875rem;
    }

    /* Grupos de botões em mobile */
    .btn-group-mobile {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .btn-group-mobile .btn {
        width: 100%;
        margin: 0;
    }

    /* Select2 responsivo */
    .select2-container .select2-selection--single {
        height: 48px !important;
        line-height: 48px !important;
    }

    .select2-container .select2-selection--single .select2-selection__rendered {
        padding-left: 15px !important;
        padding-right: 40px !important;
    }

    .select2-container .select2-selection--single .select2-selection__arrow {
        height: 46px !important;
        right: 10px !important;
    }
}

/* ========================================
   CARDS E COMPONENTES
   ======================================== */
@media (max-width: 767.98px) {
    .card {
        margin-bottom: 1rem;
    }

    .card-header {
        padding: 1rem 15px;
    }

    .card-body {
        padding: 1rem 15px;
    }

    .card-footer {
        padding: 0.75rem 15px;
    }

    /* Cards de métricas responsivos */
    .dashboard-metric-card .card-body {
        padding: 1rem;
    }

    .metric-number {
        font-size: 1.5rem !important;
    }

    .metric-label {
        font-size: 0.8rem !important;
    }

    .metric-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 20px !important;
    }
}

/* ========================================
   GRID SYSTEM MELHORADO
   ======================================== */
@media (max-width: 575.98px) {
    .col-xs-12 {
        width: 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }

    /* Força colunas a ocuparem largura total em telas muito pequenas */
    .row-mobile-stack > [class*="col-"] {
        width: 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 1rem;
    }
}

/* ========================================
   MODAIS RESPONSIVOS
   ======================================== */
@media (max-width: 767.98px) {
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .modal-header {
        padding: 1rem 15px;
    }

    .modal-body {
        padding: 1rem 15px;
    }

    .modal-footer {
        padding: 0.75rem 15px;
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* ========================================
   TIPOGRAFIA RESPONSIVA
   ======================================== */
@media (max-width: 767.98px) {
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.1rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.9rem; }

    .display-1 { font-size: 2.5rem; }
    .display-2 { font-size: 2rem; }
    .display-3 { font-size: 1.75rem; }
    .display-4 { font-size: 1.5rem; }
}

/* ========================================
   ESPAÇAMENTOS RESPONSIVOS
   ======================================== */
@media (max-width: 767.98px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .p-mobile-2 { padding: 0.5rem !important; }
    .px-mobile-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
    .py-mobile-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }

    .m-mobile-2 { margin: 0.5rem !important; }
    .mx-mobile-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }
    .my-mobile-2 { margin-top: 0.5rem !important; margin-bottom: 0.5rem !important; }
}

/* ========================================
   MELHORIAS DE USABILIDADE MOBILE
   ======================================== */
@media (max-width: 767.98px) {
    /* Área de toque melhorada */
    .btn, .form-control, .custom-select, .page-link {
        min-height: 44px;
    }

    /* Links e botões com área de toque adequada */
    a, button, .btn, .clickable {
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Scroll suave */
    html {
        scroll-behavior: smooth;
    }

    /* Evita zoom horizontal */
    body {
        overflow-x: hidden;
    }
}